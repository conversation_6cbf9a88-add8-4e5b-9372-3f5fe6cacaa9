<div id="slideshow-or-results">
  {% if api_recipes %}
  <!-- Inspiration-based Results Section -->
  <div class="results-section" id="resultsSection">

    <!-- Scroll Expansion Indicator - Sticky and Prominent -->
    <div class="scroll-expansion-indicator" id="scroll-expansion-indicator">
      <div class="scroll-hint">
        <span class="scroll-text">Click for full-screen view</span>
        <div class="scroll-arrow">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7 10L12 15L17 10" stroke="#4CAF50" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- Concise Single-Line Header -->
    <div class="concise-header text-center mb-4">
      <h1 class="concise-title">🍽️ Found {{ total_recipes_count|default:api_recipes|length }} Recipes - Showing results for '{{ search_term }}' - {{ total_recipes_count|default:api_recipes|length }} recipes found - Recipe sources: 🆓 TheMealDB Spoonacular Tasty</h1>
    </div>

    <!-- Featured Recipe Section -->
    {% if featured_recipe_details %}
    <div class="featured-recipe-section mb-5">
      <!-- Recipe Header -->
      <div class="featured-recipe-header">
        <h2 class="featured-recipe-title">{{ featured_recipe_details.title }}</h2>
        <p class="featured-recipe-subtitle">
          Source: {{ featured_recipe_details.source|title }} |
          Ingredients: {{ featured_recipe_details.ingredients|length }} |
          Instructions: {{ featured_recipe_details.instructions|length }}
        </p>

        <!-- Recipe Meta Information -->
        <div class="featured-recipe-meta">
          {% if featured_recipe_details.readyInMinutes %}
          <span class="meta-item">⏱️ {{ featured_recipe_details.readyInMinutes }} minutes</span>
          {% endif %}
          {% if featured_recipe_details.servings %}
          <span class="meta-item">👥 {{ featured_recipe_details.servings }} servings</span>
          {% endif %}
          {% if featured_recipe_details.source %}
          <span class="meta-item source-badge source-{{ featured_recipe_details.source }}">{{ featured_recipe_details.source|title }}</span>
          {% endif %}
        </div>

        <!-- Nutritional Information -->
        {% if featured_recipe_nutrition.nutrition %}
        <div class="featured-nutrition">
          <h3 class="nutrition-title">📊 Nutritional Information</h3>
          <div class="nutrition-grid">
            {% for nutrient, data in featured_recipe_nutrition.nutrition.items %}
            {% if data.amount and nutrient in "Energy,Protein,Total lipid (fat),Carbohydrate, by difference,Fiber, total dietary" %}
            <div class="nutrition-item">
              <span class="nutrient-name">{{ nutrient|title }}</span>
              <span class="nutrient-value">{{ data.amount|floatformat:1 }} {{ data.unit|default:"g" }}</span>
            </div>
            {% endif %}
            {% endfor %}
          </div>
        </div>
        {% endif %}

        <!-- Additional Details -->
        <div class="featured-additional">
          {% if featured_recipe_details.category %}
          <span class="detail-tag">Category: {{ featured_recipe_details.category }}</span>
          {% endif %}
          {% if featured_recipe_details.area %}
          <span class="detail-tag">Cuisine: {{ featured_recipe_details.area }}</span>
          {% endif %}
          {% if featured_recipe_details.tags %}
          <div class="recipe-tags-featured">
            {% for tag in featured_recipe_details.tags %}
            {% if tag %}
            <span class="tag-featured">{{ tag }}</span>
            {% endif %}
            {% endfor %}
          </div>
          {% endif %}
        </div>
      </div>

      <!-- Three-Column Layout Container -->
      <div class="featured-recipe-container">
        <!-- Column 1: Recipe Image -->
        <div class="featured-recipe-image">
          {% if featured_recipe_details.image_url %}
          <img src="{{ featured_recipe_details.image_url }}" alt="{{ featured_recipe_details.title }}" style="filter: saturate(1.2);">
          {% else %}
          <div class="featured-recipe-placeholder"></div>
          {% endif %}
        </div>

        <!-- Column 2: Ingredients List -->
        <div class="featured-recipe-ingredients">
          {% if featured_recipe_details.ingredients %}
            <div class="featured-ingredients">
            <h3 class="ingredients-title">🥘 Ingredients</h3>
            <ul class="ingredients-list">
              {% for ingredient in featured_recipe_details.ingredients %}
              {% if ingredient %}
              <li>{{ ingredient }}</li>
              {% endif %}
              {% endfor %}
            </ul>
          </div>
          {% else %}
          <div class="featured-ingredients">
            <h3 class="ingredients-title">🥘 Ingredients</h3>
            <p class="no-data-message">Ingredients not available for this recipe.</p>
          </div>
          {% endif %}
        </div>

        <!-- Column 3: Instructions -->
        <div class="featured-recipe-instructions">
          {% if featured_recipe_details.instructions %}
          <div class="featured-instructions">
            <h3 class="instructions-title">👨‍🍳 Instructions</h3>
            <ol class="instructions-list">
              {% for instruction in featured_recipe_details.instructions %}
              {% if instruction %}
              <li>{{ instruction|striptags }}</li>
              {% endif %}
              {% endfor %}
            </ol>
          </div>
          {% else %}
          <div class="featured-instructions">
            <h3 class="instructions-title">👨‍🍳 Instructions</h3>
            <p class="no-data-message">Cooking instructions not available for this recipe.</p>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
    {% endif %}

    <!-- Featured Recipes Carousel (Prioritizing TheMealDB) -->
    <div class="carousel-container" id="featured-carousel-section">
      <div class="section-header">
        <h2 class="section-title">🔥 Featured Recipes</h2>
        <span class="section-subtitle">Discover amazing recipes</span>
      </div>
      {% if api_recipes|length > 0 %}
      <div class="carousel" id="recipe-carousel">
        {% for recipe in api_recipes %}
        <div class="recipe-card" data-recipe-url="{% if recipe.combined_id %}{% url 'recipes:recipe_detail_api' recipe.combined_id %}{% else %}#{% endif %}"
             data-idx="{{ forloop.counter0 }}" style="cursor: pointer;">
          {% if recipe.image_url %}
          <img src="{{ recipe.image_url }}" alt="{{ recipe.title }}" class="recipe-image"
               style="filter: saturate(1.2);">
          {% else %}
          <div class="recipe-image" style="background: linear-gradient(45deg, #ff6b35, #ff8c69);"></div>
          {% endif %}

          <div class="recipe-content">
            <h3 class="recipe-title">{{ recipe.title|truncatechars:45 }}</h3>

            <div class="recipe-meta">
              {% if recipe.readyInMinutes %}
              <span>⏱️ {{ recipe.readyInMinutes }} min</span>
              {% endif %}
              {% if recipe.servings %}
              <span>👥 {{ recipe.servings }} servings</span>
              {% endif %}
              {% if recipe.source == 'themealdb' %}
              <span>⭐ Free Recipe</span>
              {% endif %}
            </div>

            <div class="recipe-tags">
              <!-- Prioritize TheMealDB badges -->
              {% if recipe.source == 'themealdb' %}
                <span class="tag themealdb-tag">🆓 Free</span>
              {% endif %}
              {% if recipe.area %}
                <span class="tag">{{ recipe.area }}</span>
              {% endif %}
              {% if recipe.category %}
                <span class="tag">{{ recipe.category }}</span>
              {% endif %}
              <!-- Show first few ingredients for other sources -->
              {% if recipe.source != 'themealdb' and recipe.ingredients %}
                {% for ingredient in recipe.ingredients|slice:":2" %}
                <span class="tag">{{ ingredient|truncatechars:15 }}</span>
                {% endfor %}
              {% endif %}
            </div>
          </div>
        </div>
        {% endfor %}
      </div>

      <button class="carousel-nav prev" onclick="scrollCarousel('recipe-carousel', -300)">
        <svg viewBox="0 0 24 24"><path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/></svg>
      </button>
      <button class="carousel-nav next" onclick="scrollCarousel('recipe-carousel', 300)">
        <svg viewBox="0 0 24 24"><path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/></svg>
      </button>
      {% else %}
      <div class="alert alert-info">
        <p class="mb-0">No recipes found. Try different search terms.</p>
      </div>
      {% endif %}
    </div>



    <script>
      // Carousel functionality
      function scrollCarousel(carouselId, scrollAmount) {
        const carousel = document.getElementById(carouselId);
        if (carousel) {
          carousel.scrollBy({
            left: scrollAmount,
            behavior: 'smooth'
          });
        }
      }



      // Add scroll shadows to carousels
      document.querySelectorAll('.carousel').forEach(carousel => {
        carousel.addEventListener('scroll', function() {
          const container = this.parentElement;
          const scrollLeft = this.scrollLeft;
          const maxScroll = this.scrollWidth - this.clientWidth;

          // Update navigation button visibility
          const prevBtn = container.querySelector('.carousel-nav.prev');
          const nextBtn = container.querySelector('.carousel-nav.next');

          if (prevBtn && nextBtn) {
            prevBtn.style.opacity = scrollLeft > 0 ? '1' : '0.5';
            nextBtn.style.opacity = scrollLeft < maxScroll ? '1' : '0.5';
          }
        });
      });

      // Initialize carousel navigation visibility
      document.querySelectorAll('.carousel').forEach(carousel => {
        const container = carousel.parentElement;
        const prevBtn = container.querySelector('.carousel-nav.prev');
        if (prevBtn) {
          prevBtn.style.opacity = '0.5';
        }
      });




    </script>
  {% else %}
  <!-- <div class="alert alert-warning">
    <h4 class="alert-heading">No Recipes Found</h4>
    <p>We couldn't find any recipes matching your search. Try different ingredients, cuisine type, or check your
      connection.</p>
  </div> -->
  {% endif %}
</div>

<style>
  /* Inspiration-based Results Section */
  .results-section {
    padding: 60px 0;
    opacity: 0;
    transform: translateY(20px);
    animation: slideUp 0.6s ease forwards;
    min-height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  @keyframes slideUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Concise Header Styles */
  .concise-header {
    background: linear-gradient(135deg, #ff6b35, #ff8c69);
    color: white;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(255, 107, 53, 0.3);
  }

  .concise-title {
    text-align: left;
    font-size: 15px;
    line-height: 1.5;
    color: white;
    font-weight: 600;
    margin: 0;
    line-height: 1.4;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  /* Featured Recipe Section Styles */
  .featured-recipe-section {
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(76, 175, 80, 0.15);
    overflow: hidden;
    margin-bottom: 40px;
    border: 2px solid #4CAF50;
  }

  /* Featured Recipe Header */
  .featured-recipe-header {
    padding: 30px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #4CAF50;
  }

  .featured-recipe-title {
    font-size: 28px;
    font-weight: 700;
    color: #212529;
    margin-bottom: 10px;
    line-height: 1.2;
    border-bottom: 3px solid #4CAF50;
    padding-bottom: 10px;
  }

  .featured-recipe-subtitle {
    color: #666;
    font-size: 14px;
    margin: 10px 0 20px 0;
  }

  /* Three-Column Layout Container */
  .featured-recipe-container {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 0;
    min-height: 500px;
  }

  /* Column 1: Recipe Image */
  .featured-recipe-image {
    position: relative;
    overflow: hidden;
    border-right: 1px solid #4CAF50;
    box-shadow: inset 0 0 20px rgba(76, 175, 80, 0.1);
  }

  .featured-recipe-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .featured-recipe-image:hover img {
    transform: scale(1.05);
  }

  .featured-recipe-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #4CAF50, #66BB6A, #81C784);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    font-weight: 600;
  }

  /* Column 2: Ingredients */
  .featured-recipe-ingredients {
    padding: 30px;
    overflow-y: auto;
    max-height: 500px;
    border-right: 1px solid #4CAF50;
    box-shadow: inset 0 0 20px rgba(76, 175, 80, 0.1);
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  }

  /* Column 3: Instructions */
  .featured-recipe-instructions {
    padding: 30px;
    overflow-y: auto;
    max-height: 500px;
    box-shadow: inset 0 0 20px rgba(76, 175, 80, 0.1);
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  }

  .featured-recipe-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
  }

  .meta-item {
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    color: #495057;
    border: 1px solid #4CAF50;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
  }

  .source-badge {
    background: #4CAF50 !important;
    color: white !important;
    border: 1px solid #4CAF50 !important;
  }

  .featured-ingredients,
  .featured-nutrition,
  .featured-instructions,
  .featured-additional {
    margin-bottom: 25px;
  }

  .ingredients-title,
  .nutrition-title,
  .instructions-title {
    font-size: 18px;
    font-weight: 600;
    color: #4CAF50;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
    border-bottom: 2px solid #4CAF50;
    padding-bottom: 8px;
  }

  .ingredients-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .ingredients-list li {
    background: #f8f9fa;
    padding: 12px 15px;
    margin-bottom: 10px;
    border-radius: 8px;
    border-left: 4px solid #4CAF50;
    font-size: 14px;
    line-height: 1.5;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
    transition: all 0.2s ease;
  }

  .ingredients-list li:hover {
    background: #e8f5e8;
    transform: translateX(2px);
  }

  .nutrition-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 12px;
  }

  .nutrition-item {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #4CAF50;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
    transition: all 0.2s ease;
  }

  .nutrition-item:hover {
    background: #e8f5e8;
    transform: translateY(-1px);
  }

  .nutrient-name {
    font-weight: 500;
    color: #495057;
    font-size: 13px;
  }

  .nutrient-value {
    font-weight: 600;
    color: #4CAF50;
    font-size: 14px;
  }

  .instructions-list {
    padding-left: 20px;
    margin: 0;
    counter-reset: step-counter;
  }

  .instructions-list li {
    margin-bottom: 15px;
    line-height: 1.6;
    font-size: 14px;
    color: #495057;
    background: #f8f9fa;
    padding: 12px 15px;
    border-radius: 8px;
    border-left: 4px solid #4CAF50;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
    position: relative;
    list-style: none;
    counter-increment: step-counter;
    transition: all 0.2s ease;
  }

  .instructions-list li:hover {
    background: #e8f5e8;
    transform: translateX(2px);
  }

  .instructions-list li::before {
    content: counter(step-counter);
    position: absolute;
    left: -25px;
    top: 12px;
    background: #4CAF50;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
  }

  .detail-tag {
    background: #e8f5e8;
    color: #2e7d32;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 8px;
    margin-bottom: 8px;
    display: inline-block;
    border: 1px solid #4CAF50;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
    transition: all 0.2s ease;
  }

  .detail-tag:hover {
    background: #c8e6c9;
    transform: translateY(-1px);
  }

  .recipe-tags-featured {
    margin-top: 15px;
  }

  .tag-featured {
    background: #e8f5e8;
    color: #4CAF50;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 8px;
    margin-bottom: 8px;
    display: inline-block;
    border: 1px solid #4CAF50;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
    transition: all 0.2s ease;
  }

  .tag-featured:hover {
    background: #c8e6c9;
    transform: translateY(-1px);
  }

  .no-data-message {
    color: #6c757d;
    font-style: italic;
    font-size: 14px;
    margin: 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #4CAF50;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
  }

  .no-data-message a {
    color: #4CAF50;
    text-decoration: none;
    font-weight: 500;
  }

  .no-data-message a:hover {
    text-decoration: underline;
  }

  /* Enhanced Results Header (Inspiration-style) */
  .results-header {
    text-align: center;
    padding: 40px 0;
    background: white;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 2rem;
  }

  .section-title {
    color: #ff6b35;
    font-size: 48px;
    font-weight: 300;
    margin-bottom: 12px;
    letter-spacing: -1px;
  }

  .search-subtitle {
    color: #6c757d;
    font-size: 18px;
    margin-bottom: 40px;
  }

  /* Recipe Count Display */
  .recipe-count-display {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
  }

  .recipe-count-badge {
    background: linear-gradient(135deg, #ff6b35, #ff8c69);
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    font-weight: 500;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
  }

  /* Section Headers (Inspiration-style) */
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
  }

  .section-header .section-title {
    font-size: 24px;
    font-weight: 600;
    color: #212529;
    margin-bottom: 0;
  }

  .view-all {
    color: #ff6b35;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: color 0.3s ease;
  }

  .view-all:hover {
    color: #e55a2b;
  }

  .section-subtitle {
    color: #6c757d;
    font-size: 14px;
    font-weight: 400;
    margin: 0;
  }

  /* Horizontal Carousel (Inspiration-style) */
  .carousel-container {
    position: relative;
    margin-bottom: 50px;
  }

  .carousel {
    display: flex;
    gap: 20px;
    overflow-x: auto;
    padding: 20px 0;
    scroll-behavior: smooth;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .carousel::-webkit-scrollbar {
    display: none;
  }

  .recipe-card {
    min-width: 280px;
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .recipe-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }

  .recipe-image {
    width: 100%;
    height: 180px;
    object-fit: cover;
    background: linear-gradient(45deg, #ff6b35, #ff8c69);
  }

  .recipe-content {
    padding: 20px;
  }

  .recipe-title {
    font-size: 18px;
    font-weight: 600;
    color: #212529;
    margin-bottom: 8px;
    line-height: 1.3;
  }

  .recipe-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 12px;
  }

  .recipe-tags {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
  }

  .tag {
    background: #fff3f0;
    color: #ff6b35;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
  }

  .themealdb-tag {
    background: #fff3f0;
    color: #ff6b35;
    font-weight: 600;
  }





  /* Load More Button */
  .load-more-section {
    margin-top: 40px;
  }

  .btn-load-more {
    background: linear-gradient(135deg, #ff6b35, #ff8c69);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
  }

  .btn-load-more:hover {
    background: linear-gradient(135deg, #e55a2b, #ff6b35);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(255, 107, 53, 0.4);
  }

  .load-more-spinner {
    display: inline-block;
    width: 14px;
    height: 14px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
  }

  @keyframes spin {
    to { transform: rotate(360deg); }
  }

  /* Carousel Navigation (Inspiration-style) */
  .carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: white;
    border: none;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    z-index: 2;
  }

  .carousel-nav:hover {
    background: #ff6b35;
    color: white;
  }

  .carousel-nav.prev {
    left: -20px;
  }

  .carousel-nav.next {
    right: -20px;
  }

  .carousel-nav svg {
    width: 20px;
    height: 20px;
  }

  /* API Source Legend */
  .api-source-legend {
    background: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1rem;
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: inline-block;
    backdrop-filter: blur(10px);
  }

  /* API Source Badges */
  .api-source-badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.6rem;
    border-radius: 15px;
    font-weight: 600;
    color: white !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
  }

  .api-source-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  }

  .api-source-spoonacular {
    background: linear-gradient(135deg, #4CAF50, #45a049) !important;
  }

  .api-source-themealdb {
    background: linear-gradient(135deg, #FF9800, #f57c00) !important;
  }

  .api-source-tasty {
    background: linear-gradient(135deg, #E91E63, #c2185b) !important;
  }

  /* Source badge positioning in cards */
  .card-img-overlay-source .api-source-badge {
    font-size: 0.7rem;
    padding: 0.3rem 0.5rem;
    backdrop-filter: blur(5px);
    background: rgba(0,0,0,0.1) !important;
    border: 1px solid rgba(255,255,255,0.3);
  }

  .card-img-overlay-source .api-source-spoonacular {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.9), rgba(69, 160, 73, 0.9)) !important;
  }

  .card-img-overlay-source .api-source-themealdb {
    background: linear-gradient(135deg, rgba(255, 152, 0, 0.9), rgba(245, 124, 0, 0.9)) !important;
  }

  .card-img-overlay-source .api-source-tasty {
    background: linear-gradient(135deg, rgba(233, 30, 99, 0.9), rgba(194, 24, 91, 0.9)) !important;
  }

  /* Enhanced Carousel Cards */
  .enhanced-card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .enhanced-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
  }

  .recipe-meta {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 0.75rem;
  }

  .recipe-meta i {
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
  }

  .ingredient-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .ingredient-tag {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    border: 1px solid #dee2e6;
  }

  .tag-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    border: 1px solid #FF6B35;
    color: #FF6B35;
    background-color: transparent;
  }

  .ingredients-list {
    padding-left: 1.2rem;
    margin-bottom: 0.5rem;
  }

  /* Vertical Grid Styles */
  .recipe-grid-wrapper {
    animation: fadeIn 0.5s ease-in-out;
    padding: 1rem 0;
  }

  .recipe-grid-wrapper .row {
    margin: 0 -0.75rem;
  }

  .recipe-grid-wrapper .col-lg-4,
  .recipe-grid-wrapper .col-md-6,
  .recipe-grid-wrapper .col-sm-12 {
    padding: 0 0.75rem;
    margin-bottom: 1.5rem;
  }

  .recipe-grid-card {
    border: none;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  }

  .recipe-grid-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(0,0,0,0.15);
  }

  .recipe-grid-card .card-title {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
    line-height: 1.3;
    margin-bottom: 0.75rem;
  }

  .recipe-meta-compact {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 0.5rem;
  }

  .ingredient-tags-compact {
    display: flex;
    flex-wrap: wrap;
    gap: 0.2rem;
  }

  .ingredient-tags-compact .badge {
    font-size: 0.65rem;
    padding: 0.15rem 0.4rem;
    border-radius: 8px;
  }

  /* Enhanced Carousel Styles */
  .carousel-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.9), rgba(255, 107, 53, 0.7));
    color: white;
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
  }

  .carousel-arrow:hover {
    background: linear-gradient(135deg, #FF6B35, #e55a2b);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 16px rgba(255, 107, 53, 0.4);
  }

  .carousel-arrow.left {
    left: 15px;
  }

  .carousel-arrow.right {
    right: 15px;
  }

  .active-card {
    transform: scale(1.05);
    z-index: 5;
    box-shadow: 0 12px 30px rgba(0,0,0,0.15);
  }

  .inactive-card {
    transform: scale(0.95);
    opacity: 0.85;
  }

  /* Animations */
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .recipe-carousel-wrapper {
    animation: fadeIn 0.5s ease-in-out;
  }

  /* Responsive Design (Inspiration-style) */
  @media (max-width: 768px) {
    .results-section {
      padding: 30px 0;
      min-height: 100%;
      width: 100%;
      display: flex;
      flex-direction: column;
    }

    .section-title {
      font-size: 36px;
    }

    .section-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .carousel-nav {
      display: none;
    }



    .recipe-count-badge {
      font-size: 13px;
      padding: 6px 16px;
    }

    /* Mobile API source legend */
    .api-source-legend {
      padding: 0.5rem 0.75rem;
      border-radius: 15px;
      font-size: 0.8rem;
    }

    .api-source-legend .api-source-badge {
      font-size: 0.65rem;
      padding: 0.2rem 0.4rem;
      margin: 0 0.1rem;
    }

    /* Mobile Concise Header */
    .concise-header {
      padding: 15px;
      margin-bottom: 20px;
    }


    /* Mobile Featured Recipe */
    .featured-recipe-header {
      padding: 20px;
    }

    .featured-recipe-container {
      grid-template-columns: 1fr;
      min-height: auto;
      gap: 0;
    }

    .featured-recipe-image {
      border-right: none;
      border-bottom: 1px solid #4CAF50;
      min-height: 250px;
    }

    .featured-recipe-ingredients,
    .featured-recipe-instructions {
      padding: 20px;
      max-height: none;
      border-right: none;
      border-bottom: 1px solid #4CAF50;
    }

    .featured-recipe-instructions {
      border-bottom: none;
    }

    .featured-recipe-title {
      font-size: 22px;
      margin-bottom: 15px;
    }

    .featured-recipe-subtitle {
      font-size: 13px;
    }

    .featured-recipe-meta {
      gap: 10px;
      margin-bottom: 20px;
    }

    .meta-item {
      padding: 6px 10px;
      font-size: 12px;
    }

    .ingredients-title,
    .nutrition-title,
    .instructions-title {
      font-size: 16px;
      margin-bottom: 12px;
    }

    .ingredients-list li {
      padding: 10px 12px;
      font-size: 13px;
      margin-bottom: 8px;
    }

    .instructions-list li {
      padding: 10px 12px;
      font-size: 13px;
      margin-bottom: 10px;
    }

    .instructions-list li::before {
      left: -20px;
      top: 10px;
      width: 18px;
      height: 18px;
      font-size: 11px;
    }

    .nutrition-grid {
      grid-template-columns: 1fr;
      gap: 8px;
    }

    .nutrition-item {
      padding: 10px;
    }

    .nutrient-name {
      font-size: 12px;
    }

    .nutrient-value {
      font-size: 13px;
    }

    .instructions-list li {
      font-size: 13px;
      margin-bottom: 10px;
    }

    .detail-tag,
    .tag-featured {
      font-size: 11px;
      padding: 4px 8px;
      margin-right: 6px;
      margin-bottom: 6px;
    }
  }

  /* Tablet Responsive Design */
  @media (min-width: 769px) and (max-width: 1024px) {
    .featured-recipe-container {
      grid-template-columns: 1fr 1fr;
      gap: 0;
    }

    .featured-recipe-image {
      grid-row: 1 / 3;
      border-right: 1px solid #4CAF50;
      border-bottom: none;
    }

    .featured-recipe-ingredients {
      border-right: none;
      border-bottom: 1px solid #4CAF50;
      padding: 25px;
    }

    .featured-recipe-instructions {
      border-bottom: none;
      padding: 25px;
    }

    .featured-recipe-header {
      padding: 25px;
    }

    .ingredients-title,
    .nutrition-title,
    .instructions-title {
      font-size: 17px;
    }

    .featured-recipe-title {
      font-size: 24px;
    }

    .instructions-list li::before {
      left: -22px;
    }
  }

  /* Loading States (Inspiration-style) */
  .skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }

  @keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
  }

  /* Smooth transitions for all interactive elements */
  .results-section,
  .recipe-count-display,
  .api-source-legend,
  .recipe-card,
  .api-source-badge {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .recipe-card,
    .api-source-badge,
    .carousel-nav {
      transition: none;
      animation: none;
    }

    .results-section {
      animation: none;
      opacity: 1;
      transform: none;
    }
  }

  /* Scroll Expansion Indicator Styles - Sticky and Always Visible */
  .scroll-expansion-indicator {
    position: sticky;
    top: 10px;
    float: right;
    margin: 10px 20px 20px 0;
    z-index: 999;
    background: rgba(76, 175, 80, 0.95);
    color: white;
    padding: 12px 16px;
    border-radius: 25px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    cursor: pointer;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    clear: both;
  }

  .scroll-expansion-indicator:hover {
    background: rgba(76, 175, 80, 1);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }

  .scroll-hint {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
  }

  .scroll-text {
    white-space: nowrap;
  }

  .scroll-arrow {
    display: flex;
    align-items: center;
    animation: bounce 2s infinite;
  }

  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-3px);
    }
    60% {
      transform: translateY(-1px);
    }
  }

  /* Hide indicator when results are expanded */
  body.recipe-results-expanded .scroll-expansion-indicator {
    display: none;
  }

  /* Hide full screen view button on desktop/tablet screens - only show on mobile */
  @media (min-width: 769px) {
    .scroll-expansion-indicator {
      display: none !important;
    }
  }

  /* Mobile responsive adjustments - only show on mobile screens */
  @media (max-width: 768px) {
    .scroll-expansion-indicator {
      position: sticky;
      top: 5px;
      float: none;
      margin: 10px auto 20px auto;
      padding: 10px 14px;
      text-align: center;
      width: fit-content;
      max-width: 90%;
    }

    .scroll-hint {
      font-size: 13px;
    }

    .scroll-text {
      display: inline; /* Show full text on mobile screens */
      white-space: nowrap;
    }
  }

  @media (max-width: 480px) {
    .scroll-expansion-indicator {
      padding: 8px 12px;
      font-size: 12px;
    }

    .scroll-hint {
      font-size: 12px;
    }

    .scroll-text {
      display: inline; /* Keep full text visible even on very small screens */
    }
  }
</style>